version: '3.8'

services:
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: bookstore_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d bookstore_test"]
      interval: 5s
      timeout: 3s
      retries: 5

  test:
    build: .
    command: go test -v ./...
    environment:
      DB_HOST: postgres-test
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: bookstore_test
    depends_on:
      postgres-test:
        condition: service_healthy