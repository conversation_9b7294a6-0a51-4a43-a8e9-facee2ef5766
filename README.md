# Bookstore CRUD API

A simple RESTful API for managing a bookstore built with Go using only the standard `net/http` package and PostgreSQL for persistence.

## Features

- Full CRUD operations for books (Create, Read, Update, Delete)
- PostgreSQL database with automatic table creation
- Comprehensive test suite
- Swagger/OpenAPI documentation
- Docker containerization
- Health check endpoint
- JSON API responses with consistent structure

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/books` | Get all books |
| GET | `/books/{id}` | Get a specific book by ID |
| POST | `/books` | Create a new book |
| PUT | `/books/{id}` | Update an existing book |
| DELETE | `/books/{id}` | Delete a book |
| GET | `/health` | Health check |
| GET | `/swagger` | API documentation |

## Book Model

```json
{
  "id": 1,
  "title": "The Go Programming Language",
  "author": "<PERSON>",
  "isbn": "978-**********",
  "price": 39.99,
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository
2. Start the services:
   ```bash
   make docker-up
   ```
   or
   ```bash
   docker-compose up -d
   ```

3. The API will be available at `http://localhost:8080`
4. View API documentation at `http://localhost:8080/swagger`

### Local Development

1. Install PostgreSQL and create databases:
   ```sql
   CREATE DATABASE bookstore;
   CREATE DATABASE bookstore_test;
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Set environment variables (optional):
   ```bash
   export DB_HOST=localhost
   export DB_PORT=5432
   export DB_USER=postgres
   export DB_PASSWORD=postgres
   export DB_NAME=bookstore
   export PORT=8080
   ```

4. Run the application:
   ```bash
   make run
   ```
   or
   ```bash
   go run .
   ```

## Testing

### Run all tests:
```bash
make test
```

### Run tests with coverage:
```bash
make test-coverage
```

### Run tests in Docker:
```bash
make docker-test
```

## Example Usage

### Create a book:
```bash
curl -X POST http://localhost:8080/books \
  -H "Content-Type: application/json" \
  -d '{
    "title": "The Go Programming Language",
    "author": "Alan Donovan",
    "isbn": "978-**********",
    "price": 39.99
  }'
```

### Get all books:
```bash
curl http://localhost:8080/books
```

### Get a specific book:
```bash
curl http://localhost:8080/books/1
```

### Update a book:
```bash
curl -X PUT http://localhost:8080/books/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Title",
    "author": "Updated Author",
    "isbn": "978-**********",
    "price": 49.99
  }'
```

### Delete a book:
```bash
curl -X DELETE http://localhost:8080/books/1
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| DB_HOST | localhost | PostgreSQL host |
| DB_PORT | 5432 | PostgreSQL port |
| DB_USER | postgres | PostgreSQL username |
| DB_PASSWORD | postgres | PostgreSQL password |
| DB_NAME | bookstore | PostgreSQL database name |
| PORT | 8080 | Server port |

## Project Structure

```
bookstore-api/
├── main.go              # Main application code
├── main_test.go         # Test suite
├── go.mod               # Go module file
├── go.sum               # Go dependencies
├── Dockerfile           # Docker image definition
├── docker-compose.yml   # Docker services configuration
├── docker-compose.test.yml # Test environment
├── Makefile            # Build and development commands
└── README.md           # This file
```

## API Response Format

All API responses follow this consistent structure:

### Success Response:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... }
}
```

### Error Response:
```json
{
  "success": false,
  "error": "Error message"
}
```

## Development

### Available Make Commands:

- `make build` - Build the application binary
- `make run` - Run the application locally
- `make test` - Run tests
- `make test-coverage` - Run tests with coverage report
- `make docker-up` - Start services with Docker Compose
- `make docker-down` - Stop Docker services
- `make docker-build` - Build and start services
- `make docker-test` - Run tests in Docker
- `make logs` - View API logs
- `make clean` - Clean up build artifacts and Docker resources
- `make setup` - Setup local development environment

## Health Check

The API includes a health check endpoint at `/health` that returns the current status and timestamp:

```bash
curl http://localhost:8080/health
```

Response:
```json
{
  "status": "healthy",
  "time": "2023-01-01T12:00:00Z"
}