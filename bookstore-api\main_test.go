// main_test.go
package main

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	_ "github.com/lib/pq"
)

var testBookStore *BookStore

func TestMain(m *testing.M) {
	// Set up test database
	os.Setenv("DB_NAME", "bookstore_test")
	
	db, err := initDB()
	if err != nil {
		panic("Failed to connect to test database: " + err.Error())
	}
	
	testBookStore = NewBookStore(db)
	
	// Run tests
	code := m.Run()
	
	// Clean up
	db.Close()
	os.Exit(code)
}

func clearBooksTable(t *testing.T) {
	_, err := testBookStore.db.Exec("DELETE FROM books")
	if err != nil {
		t.Fatal("Failed to clear books table:", err)
	}
}

func TestCreateBook(t *testing.T) {
	clearBooksTable(t)
	
	book := Book{
		Title:  "Test Book",
		Author: "Test Author",
		ISBN:   "978-0123456789",
		Price:  29.99,
	}
	
	bookJSON, _ := json.Marshal(book)
	req := httptest.NewRequest(http.MethodPost, "/books", bytes.NewBuffer(bookJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	
	testBookStore.handleBooks(w, req)
	
	if w.Code != http.StatusCreated {
		t.Errorf("Expected status code %d, got %d", http.StatusCreated, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if !response.Success {
		t.Errorf("Expected success to be true, got %v", response.Success)
	}
	
	// Verify book was created in database
	createdBook := response.Data.(map[string]interface{})
	if createdBook["title"] != book.Title {
		t.Errorf("Expected title %s, got %s", book.Title, createdBook["title"])
	}
}

func TestGetAllBooks(t *testing.T) {
	clearBooksTable(t)
	
	// Create test books
	book1 := Book{Title: "Book 1", Author: "Author 1", ISBN: "978-0111111111", Price: 19.99}
	book2 := Book{Title: "Book 2", Author: "Author 2", ISBN: "978-0222222222", Price: 24.99}
	
	testBookStore.CreateBook(&book1)
	testBookStore.CreateBook(&book2)
	
	req := httptest.NewRequest(http.MethodGet, "/books", nil)
	w := httptest.NewRecorder()
	
	testBookStore.handleBooks(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if !response.Success {
		t.Errorf("Expected success to be true, got %v", response.Success)
	}
	
	books := response.Data.([]interface{})
	if len(books) != 2 {
		t.Errorf("Expected 2 books, got %d", len(books))
	}
}

func TestGetBookByID(t *testing.T) {
	clearBooksTable(t)
	
	// Create test book
	book := Book{Title: "Test Book", Author: "Test Author", ISBN: "978-0123456789", Price: 29.99}
	testBookStore.CreateBook(&book)
	
	req := httptest.NewRequest(http.MethodGet, "/books/1", nil)
	w := httptest.NewRecorder()
	
	testBookStore.handleBookByID(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if !response.Success {
		t.Errorf("Expected success to be true, got %v", response.Success)
	}
	
	returnedBook := response.Data.(map[string]interface{})
	if returnedBook["title"] != book.Title {
		t.Errorf("Expected title %s, got %s", book.Title, returnedBook["title"])
	}
}

func TestGetBookByIDNotFound(t *testing.T) {
	clearBooksTable(t)
	
	req := httptest.NewRequest(http.MethodGet, "/books/999", nil)
	w := httptest.NewRecorder()
	
	testBookStore.handleBookByID(w, req)
	
	if w.Code != http.StatusNotFound {
		t.Errorf("Expected status code %d, got %d", http.StatusNotFound, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if response.Success {
		t.Errorf("Expected success to be false, got %v", response.Success)
	}
}

func TestUpdateBook(t *testing.T) {
	clearBooksTable(t)
	
	// Create test book
	book := Book{Title: "Original Title", Author: "Original Author", ISBN: "978-0123456789", Price: 29.99}
	testBookStore.CreateBook(&book)
	
	// Update the book
	updatedBook := Book{Title: "Updated Title", Author: "Updated Author", ISBN: "978-0987654321", Price: 39.99}
	bookJSON, _ := json.Marshal(updatedBook)
	
	req := httptest.NewRequest(http.MethodPut, "/books/1", bytes.NewBuffer(bookJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	
	testBookStore.handleBookByID(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if !response.Success {
		t.Errorf("Expected success to be true, got %v", response.Success)
	}
	
	returnedBook := response.Data.(map[string]interface{})
	if returnedBook["title"] != updatedBook.Title {
		t.Errorf("Expected title %s, got %s", updatedBook.Title, returnedBook["title"])
	}
}

func TestDeleteBook(t *testing.T) {
	clearBooksTable(t)
	
	// Create test book
	book := Book{Title: "Test Book", Author: "Test Author", ISBN: "978-0123456789", Price: 29.99}
	testBookStore.CreateBook(&book)
	
	req := httptest.NewRequest(http.MethodDelete, "/books/1", nil)
	w := httptest.NewRecorder()
	
	testBookStore.handleBookByID(w, req)
	
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}
	
	var response APIResponse
	err := json.NewDecoder(w.Body).Decode(&response)
	if err != nil {
		t.Fatal("Failed to decode response:", err)
	}
	
	if !response.Success {
		t.Errorf("Expected success to be true, got %v", response.Success)
	}
	
	// Verify book was deleted
	_, err = testBookStore.GetBook(1)
	if err != sql.ErrNoRows {
		t.Error("Expected book to be deleted")
	}
}

func TestCreateBookInvalidJSON(t *testing.T) {
	req := httptest.NewRequest(http.MethodPost, "/books", bytes.NewBuffer([]byte("{invalid json")))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	
	testBookStore.handleBooks(w, req)
	
	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status code %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestCreateBookMissingFields(t *testing.T) {
	book := Book{Title: "Test Book"} // Missing author and ISBN
	bookJSON, _ := json.Marshal(book)
	
	

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"time"

	_ "github.com/lib/pq"
)

// Book represents a book entity
type Book struct {
	ID          int     `json:"id"`
	Title       string  `json:"title"`
	Author      string  `json:"author"`
	ISBN        string  `json:"isbn"`
	Price       float64 `json:"price"`
	CreatedAt   string  `json:"created_at"`
	UpdatedAt   string  `json:"updated_at"`
}

// BookStore handles database operations
type BookStore struct {
	db *sql.DB
}

// APIResponse represents standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// NewBookStore creates a new BookStore instance
func NewBookStore(db *sql.DB) *BookStore {
	return &BookStore{db: db}
}

// CreateBook creates a new book
func (bs *BookStore) CreateBook(book *Book) error {
	query := `
		INSERT INTO books (title, author, isbn, price, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`
	
	now := time.Now().Format(time.RFC3339)
	err := bs.db.QueryRow(query, book.Title, book.Author, book.ISBN, book.Price, now, now).Scan(&book.ID)
	if err != nil {
		return err
	}
	book.CreatedAt = now
	book.UpdatedAt = now
	return nil
}

// GetBook retrieves a book by ID
func (bs *BookStore) GetBook(id int) (*Book, error) {
	query := `SELECT id, title, author, isbn, price, created_at, updated_at FROM books WHERE id = $1`
	
	book := &Book{}
	err := bs.db.QueryRow(query, id).Scan(
		&book.ID, &book.Title, &book.Author, &book.ISBN, 
		&book.Price, &book.CreatedAt, &book.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return book, nil
}

// GetAllBooks retrieves all books
func (bs *BookStore) GetAllBooks() ([]Book, error) {
	query := `SELECT id, title, author, isbn, price, created_at, updated_at FROM books ORDER BY id`
	
	rows, err := bs.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var books []Book
	for rows.Next() {
		book := Book{}
		err := rows.Scan(&book.ID, &book.Title, &book.Author, &book.ISBN, 
			&book.Price, &book.CreatedAt, &book.UpdatedAt)
		if err != nil {
			return nil, err
		}
		books = append(books, book)
	}
	return books, nil
}

// UpdateBook updates an existing book
func (bs *BookStore) UpdateBook(id int, book *Book) error {
	query := `
		UPDATE books 
		SET title = $1, author = $2, isbn = $3, price = $4, updated_at = $5
		WHERE id = $6`
	
	now := time.Now().Format(time.RFC3339)
	result, err := bs.db.Exec(query, book.Title, book.Author, book.ISBN, book.Price, now, id)
	if err != nil {
		return err
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return sql.ErrNoRows
	}
	
	book.ID = id
	book.UpdatedAt = now
	return nil
}

// DeleteBook deletes a book by ID
func (bs *BookStore) DeleteBook(id int) error {
	query := `DELETE FROM books WHERE id = $1`
	
	result, err := bs.db.Exec(query, id)
	if err != nil {
		return err
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return sql.ErrNoRows
	}
	return nil
}

// API Handlers

func (bs *BookStore) handleBooks(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	switch r.Method {
	case http.MethodGet:
		bs.handleGetBooks(w, r)
	case http.MethodPost:
		bs.handleCreateBook(w, r)
	default:
		bs.sendErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
	}
}

func (bs *BookStore) handleBookByID(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Extract ID from URL path
	re := regexp.MustCompile(`/books/(\d+)`)
	matches := re.FindStringSubmatch(r.URL.Path)
	if len(matches) != 2 {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Invalid book ID")
		return
	}
	
	id, err := strconv.Atoi(matches[1])
	if err != nil {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Invalid book ID")
		return
	}
	
	switch r.Method {
	case http.MethodGet:
		bs.handleGetBook(w, r, id)
	case http.MethodPut:
		bs.handleUpdateBook(w, r, id)
	case http.MethodDelete:
		bs.handleDeleteBook(w, r, id)
	default:
		bs.sendErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
	}
}

func (bs *BookStore) handleGetBooks(w http.ResponseWriter, r *http.Request) {
	books, err := bs.GetAllBooks()
	if err != nil {
		bs.sendErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve books")
		return
	}
	
	bs.sendSuccessResponse(w, http.StatusOK, "Books retrieved successfully", books)
}

func (bs *BookStore) handleGetBook(w http.ResponseWriter, r *http.Request, id int) {
	book, err := bs.GetBook(id)
	if err != nil {
		if err == sql.ErrNoRows {
			bs.sendErrorResponse(w, http.StatusNotFound, "Book not found")
			return
		}
		bs.sendErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve book")
		return
	}
	
	bs.sendSuccessResponse(w, http.StatusOK, "Book retrieved successfully", book)
}

func (bs *BookStore) handleCreateBook(w http.ResponseWriter, r *http.Request) {
	var book Book
	if err := json.NewDecoder(r.Body).Decode(&book); err != nil {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}
	
	// Validate required fields
	if book.Title == "" || book.Author == "" || book.ISBN == "" {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Title, Author, and ISBN are required")
		return
	}
	
	if err := bs.CreateBook(&book); err != nil {
		bs.sendErrorResponse(w, http.StatusInternalServerError, "Failed to create book")
		return
	}
	
	bs.sendSuccessResponse(w, http.StatusCreated, "Book created successfully", book)
}

func (bs *BookStore) handleUpdateBook(w http.ResponseWriter, r *http.Request, id int) {
	var book Book
	if err := json.NewDecoder(r.Body).Decode(&book); err != nil {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}
	
	// Validate required fields
	if book.Title == "" || book.Author == "" || book.ISBN == "" {
		bs.sendErrorResponse(w, http.StatusBadRequest, "Title, Author, and ISBN are required")
		return
	}
	
	if err := bs.UpdateBook(id, &book); err != nil {
		if err == sql.ErrNoRows {
			bs.sendErrorResponse(w, http.StatusNotFound, "Book not found")
			return
		}
		bs.sendErrorResponse(w, http.StatusInternalServerError, "Failed to update book")
		return
	}
	
	bs.sendSuccessResponse(w, http.StatusOK, "Book updated successfully", book)
}

func (bs *BookStore) handleDeleteBook(w http.ResponseWriter, r *http.Request, id int) {
	if err := bs.DeleteBook(id); err != nil {
		if err == sql.ErrNoRows {
			bs.sendErrorResponse(w, http.StatusNotFound, "Book not found")
			return
		}
		bs.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete book")
		return
	}
	
	bs.sendSuccessResponse(w, http.StatusOK, "Book deleted successfully", nil)
}

func (bs *BookStore) sendSuccessResponse(w http.ResponseWriter, statusCode int, message string, data interface{}) {
	w.WriteHeader(statusCode)
	response := APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
	json.NewEncoder(w).Encode(response)
}

func (bs *BookStore) sendErrorResponse(w http.ResponseWriter, statusCode int, errorMsg string) {
	w.WriteHeader(statusCode)
	response := APIResponse{
		Success: false,
		Error:   errorMsg,
	}
	json.NewEncoder(w).Encode(response)
}

// Health check handler
func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"status": "healthy",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// Swagger documentation handler
func swaggerHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")
	html := `<!DOCTYPE html>
<html>
<head>
    <title>Bookstore API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/swagger.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>`
	w.Write([]byte(html))
}

// Swagger JSON handler
func swaggerJSONHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	swaggerSpec := `{
  "swagger": "2.0",
  "info": {
    "title": "Bookstore API",
    "description": "A simple bookstore CRUD API built with Go and PostgreSQL",
    "version": "1.0.0"
  },
  "host": "localhost:8080",
  "basePath": "/",
  "schemes": ["http"],
  "consumes": ["application/json"],
  "produces": ["application/json"],
  "paths": {
    "/books": {
      "get": {
        "summary": "Get all books",
        "description": "Retrieve a list of all books in the store",
        "responses": {
          "200": {
            "description": "Successful response",
            "schema": {
              "type": "object",
              "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "data": {
                  "type": "array",
                  "items": {"$ref": "#/definitions/Book"}
                }
              }
            }
          }
        }
      },
      "post": {
        "summary": "Create a new book",
        "description": "Add a new book to the store",
        "parameters": [{
          "in": "body",
          "name": "book",
          "description": "Book object to be added",
          "required": true,
          "schema": {"$ref": "#/definitions/BookInput"}
        }],
        "responses": {
          "201": {
            "description": "Book created successfully",
            "schema": {
              "type": "object",
              "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "data": {"$ref": "#/definitions/Book"}
              }
            }
          },
          "400": {"description": "Invalid input"}
        }
      }
    },
    "/books/{id}": {
      "get": {
        "summary": "Get a book by ID",
        "description": "Retrieve a specific book by its ID",
        "parameters": [{
          "name": "id",
          "in": "path",
          "required": true,
          "type": "integer",
          "description": "Book ID"
        }],
        "responses": {
          "200": {
            "description": "Successful response",
            "schema": {
              "type": "object",
              "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "data": {"$ref": "#/definitions/Book"}
              }
            }
          },
          "404": {"description": "Book not found"}
        }
      },
      "put": {
        "summary": "Update a book",
        "description": "Update an existing book by ID",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "type": "integer",
            "description": "Book ID"
          },
          {
            "in": "body",
            "name": "book",
            "description": "Updated book object",
            "required": true,
            "schema": {"$ref": "#/definitions/BookInput"}
          }
        ],
        "responses": {
          "200": {
            "description": "Book updated successfully",
            "schema": {
              "type": "object",
              "properties": {
                "success": {"type": "boolean"},
                "message": {"type": "string"},
                "data": {"$ref": "#/definitions/Book"}
              }
            }
          },
          "400": {"description": "Invalid input"},
          "404": {"description": "Book not found"}
        }
      },
      "delete": {
        "summary": "Delete a book",
        "description": "Remove a book from the store by ID",
        "parameters": [{
          "name": "id",
          "in": "path",
          "required": true,
          "type": "integer",
          "description": "Book ID"
        }],
        "responses": {
          "200": {"description": "Book deleted successfully"},
          "404": {"description": "Book not found"}
        }
      }
    },
    "/health": {
      "get": {
        "summary": "Health check",
        "description": "Check if the API is running",
        "responses": {
          "200": {
            "description": "API is healthy",
            "schema": {
              "type": "object",
              "properties": {
                "status": {"type": "string"},
                "time": {"type": "string"}
              }
            }
          }
        }
      }
    }
  },
  "definitions": {
    "Book": {
      "type": "object",
      "properties": {
        "id": {"type": "integer", "example": 1},
        "title": {"type": "string", "example": "The Go Programming Language"},
        "author": {"type": "string", "example": "Alan Donovan"},
        "isbn": {"type": "string", "example": "978-**********"},
        "price": {"type": "number", "example": 39.99},
        "created_at": {"type": "string", "example": "2023-01-01T12:00:00Z"},
        "updated_at": {"type": "string", "example": "2023-01-01T12:00:00Z"}
      }
    },
    "BookInput": {
      "type": "object",
      "required": ["title", "author", "isbn"],
      "properties": {
        "title": {"type": "string", "example": "The Go Programming Language"},
        "author": {"type": "string", "example": "Alan Donovan"},
        "isbn": {"type": "string", "example": "978-**********"},
        "price": {"type": "number", "example": 39.99}
      }
    }
  }
}`
	w.Write([]byte(swaggerSpec))
}

// Database initialization
func initDB() (*sql.DB, error) {
	dbHost := os.Getenv("DB_HOST")
	if dbHost == "" {
		dbHost = "localhost"
	}
	
	dbUser := os.Getenv("DB_USER")
	if dbUser == "" {
		dbUser = "postgres"
	}
	
	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		dbPassword = "postgres"
	}
	
	dbName := os.Getenv("DB_NAME")
	if dbName == "" {
		dbName = "bookstore"
	}
	
	dbPort := os.Getenv("DB_PORT")
	if dbPort == "" {
		dbPort = "5432"
	}
	
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)
	
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, err
	}
	
	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, err
	}
	
	// Create table if not exists
	createTableQuery := `
		CREATE TABLE IF NOT EXISTS books (
			id SERIAL PRIMARY KEY,
			title VARCHAR(255) NOT NULL,
			author VARCHAR(255) NOT NULL,
			isbn VARCHAR(20) UNIQUE NOT NULL,
			price DECIMAL(10,2) DEFAULT 0.00,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`
	
	if _, err := db.Exec(createTableQuery); err != nil {
		return nil, err
	}
	
	return db, nil
}

func main() {
	// Initialize database
	db, err := initDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()
	
	// Create bookstore instance
	bookStore := NewBookStore(db)
	
	// Set up routes
	http.HandleFunc("/books", bookStore.handleBooks)
	http.HandleFunc("/books/", bookStore.handleBookByID)
	http.HandleFunc("/health", healthHandler)
	http.HandleFunc("/swagger", swaggerHandler)
	http.HandleFunc("/swagger.json", swaggerJSONHandler)
	
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}
	
	log.Printf("Starting server on port %s", port)
	log.Printf("Swagger documentation available at http://localhost:%s/swagger", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}